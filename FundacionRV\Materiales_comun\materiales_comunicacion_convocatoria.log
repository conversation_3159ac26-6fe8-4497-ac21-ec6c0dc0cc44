This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.12.10)  19 JUL 2025 11:08
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./materiales_comunicacion_convocatoria.tex
(materiales_comunicacion_convocatoria.tex
LaTeX2e <2024-11-01>
L3 programming layer <2024-11-02>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\babel.sty
Package: babel 2024/11/08 v24.13 The multilingual framework for pdfLaTeX, LuaLa
TeX and XeLaTeX
\babel@savecnt=\count270
\U@D=\dimen142
\l@unhyphenated=\language79

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel\txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count271

*************************************
* Local config file bblopts.cfg used
*
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/babel-spanish\spanish.l
df
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system
\es@quottoks=\toks19
\es@quotdepth=\count272
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/babel/locale/es\babel
-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 11.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count273
\Gm@cntv=\count274
\c@Gm@tempcnt=\count275
\Gm@bindingoffset=\dimen143
\Gm@wd@mp=\dimen144
\Gm@odd@mp=\dimen145
\Gm@even@mp=\dimen146
\Gm@layoutwidth=\dimen147
\Gm@layoutheight=\dimen148
\Gm@layouthoffset=\dimen149
\Gm@layoutvoffset=\dimen150
\Gm@dimlist=\toks21

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2024/11/20 v4.4 Extensive control of page headers and footers

\f@nch@headwidth=\skip51
\f@nch@O@elh=\skip52
\f@nch@O@erh=\skip53
\f@nch@O@olh=\skip54
\f@nch@O@orh=\skip55
\f@nch@O@elf=\skip56
\f@nch@O@erf=\skip57
\f@nch@O@olf=\skip58
\f@nch@O@orf=\skip59
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen151
\Gin@req@width=\dimen152
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip60
\enit@outerparindent=\dimen153
\enit@toks=\toks22
\enit@inbox=\box52
\enit@count@id=\count276
\enitdp@description=\count277
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count278
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count279
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.s
ty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen154
\Hy@linkcounter=\count280
\Hy@pagecounter=\count281
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count282
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count283

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen155

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count284
\Field@Width=\dimen156
\Fld@charsize=\dimen157
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count285
\c@Item=\count286
\c@Hfootnote=\count287
)
Package hyperref Info: Driver (autodetected): hpdftex.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count288
\c@bookmark@seq@number=\count289

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfil
echeck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip61
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tcolorbox\tcolorbox.sty
Package: tcolorbox 2024/10/22 version 6.4.1 text color boxes

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-common.tex
\pgfutil@everybye=\toks23
\pgfutil@tempdima=\dimen158
\pgfutil@tempdimb=\dimen159
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-latex.def
\pgfutil@abb=\box53
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks24
\pgfkeys@temptoks=\toks25

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks26
))
\pgf@x=\dimen160
\pgf@y=\dimen161
\pgf@xa=\dimen162
\pgf@ya=\dimen163
\pgf@xb=\dimen164
\pgf@yb=\dimen165
\pgf@xc=\dimen166
\pgf@yc=\dimen167
\pgf@xd=\dimen168
\pgf@yd=\dimen169
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count290
\c@pgf@countb=\count291
\c@pgf@countc=\count292
\c@pgf@countd=\count293
\t@pgf@toka=\toks27
\t@pgf@tokb=\toks28
\t@pgf@tokc=\toks29
\pgf@sys@id@count=\count294

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count295
\pgfsyssoftpath@bigbuffer@items=\count296
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen170
\pgfmath@count=\count297
\pgfmath@box=\box54
\pgfmath@toks=\toks30
\pgfmath@stack@operand=\toks31
\pgfmath@stack@operation=\toks32
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count298
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.
tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen171
\pgf@picmaxx=\dimen172
\pgf@picminy=\dimen173
\pgf@picmaxy=\dimen174
\pgf@pathminx=\dimen175
\pgf@pathmaxx=\dimen176
\pgf@pathminy=\dimen177
\pgf@pathmaxy=\dimen178
\pgf@xx=\dimen179
\pgf@xy=\dimen180
\pgf@yx=\dimen181
\pgf@yy=\dimen182
\pgf@zx=\dimen183
\pgf@zy=\dimen184
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen185
\pgf@path@lasty=\dimen186
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen187
\pgf@shorten@start@additional=\dimen188
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box55
\pgf@hbox=\box56
\pgf@layerbox@main=\box57
\pgf@picture@serial@count=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen189
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen190
\pgf@pt@y=\dimen191
\pgf@pt@temp=\dimen192
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen193
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen194
\pgf@sys@shading@range@num=\count300
\pgf@shadingcount=\count301
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box59
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen195
\pgf@nodesepend=\dimen196
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.s
ty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.
code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen197
\pgffor@skip=\dimen198
\pgffor@stack=\toks33
\pgffor@toks=\toks34
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count302
\pgfplotmarksize=\dimen199
)
\tikz@lastx=\dimen256
\tikz@lasty=\dimen257
\tikz@lastxsaved=\dimen258
\tikz@lastysaved=\dimen259
\tikz@lastmovetox=\dimen260
\tikz@lastmovetoy=\dimen261
\tikzleveldistance=\dimen262
\tikzsiblingdistance=\dimen263
\tikz@figbox=\box60
\tikz@figbox@bg=\box61
\tikz@tempbox=\box62
\tikz@tempbox@bg=\box63
\tikztreelevel=\count303
\tikznumberofchildren=\count304
\tikznumberofcurrentchild=\count305
\tikz@fig@count=\count306

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
matrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count307
\pgfmatrixcurrentcolumn=\count308
\pgf@matrix@numberofcolumns=\count309
)
\tikz@expandcount=\count310

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\verbatim.sty
Package: verbatim 2024-01-22 v1.5x LaTeX2e package for verbatim enhancements
\every@verbatim=\toks35
\verbatim@line=\toks36
\verbatim@in@stream=\read4
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/trimspaces\trimspaces.s
ty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\@envbody=\toks37
)
\tcb@titlebox=\box64
\tcb@upperbox=\box65
\tcb@lowerbox=\box66
\tcb@phantombox=\box67
\c@tcbbreakpart=\count311
\c@tcblayer=\count312
\c@tcolorbox@number=\count313
\l__tcobox_tmpa_box=\box68
\l__tcobox_tmpa_dim=\dimen264
\tcb@temp=\box69
\tcb@temp=\box70
\tcb@temp=\box71
\tcb@temp=\box72
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontawesom
e5.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-11-02 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdf
tex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count314
\l__pdf_internal_box=\box73
))
Package: fontawesome5 2022/05/02 v5.15.4 Font Awesome 5

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/l3keys2e\l3k
eys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\xpars
e.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontawesom
e5-generic-helper.sty
Package: fontawesome5-generic-helper 2022/05/02 v5.15.4 non-uTeX helper for fon
tawesome5

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\fontawesom
e5-mapping.def)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count315
\mult@box=\box74
\multicol@leftmargin=\dimen265
\c@unbalance=\count316
\c@collectmore=\count317
\doublecol@number=\count318
\multicoltolerance=\count319
\multicolpretolerance=\count320
\full@width=\dimen266
\page@free=\dimen267
\premulticols=\dimen268
\postmulticols=\dimen269
\multicolsep=\skip62
\multicolbaselineskip=\skip63
\partial@page=\box75
\last@line=\box76
\mc@boxedresult=\box77
\maxbalancingoverflow=\dimen270
\mult@rightbox=\box78
\mult@grightbox=\box79
\mult@firstbox=\box80
\mult@gfirstbox=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\@tempa=\box117
\c@minrows=\count321
\c@columnbadness=\count322
\c@finalcolumnbadness=\count323
\last@try=\dimen271
\multicolovershoot=\dimen272
\multicolundershoot=\dimen273
\mult@nat@firstbox=\box118
\colbreak@box=\box119
\mc@col@check@num=\count324
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen274
\lightrulewidth=\dimen275
\cmidrulewidth=\dimen276
\belowrulesep=\dimen277
\belowbottomsep=\dimen278
\aboverulesep=\dimen279
\abovetopsep=\dimen280
\cmidrulesep=\dimen281
\cmidrulekern=\dimen282
\defaultaddspace=\dimen283
\@cmidla=\count325
\@cmidlb=\count326
\@aboverulesep=\dimen284
\@belowrulesep=\dimen285
\@thisruleclass=\count327
\@lastruleclass=\count328
\@thisrulewidth=\dimen286
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\longtable.sty
Package: longtable 2024-10-27 v4.22 Multi-page Table package (DPC)
\LTleft=\skip64
\LTright=\skip65
\LTpre=\skip66
\LTpost=\skip67
\LTchunksize=\count329
\LTcapwidth=\dimen287
\LT@head=\box120
\LT@firsthead=\box121
\LT@foot=\box122
\LT@lastfoot=\box123
\LT@gbox=\box124
\LT@cols=\count330
\LT@rows=\count331
\c@LT@tables=\count332
\c@LT@chunks=\count333
\LT@p@ftn=\toks38
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks39
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
Package hyperref Info: Option `colorlinks' set `true' on input line 37.
(materiales_comunicacion_convocatoria.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
)
\openout1 = `materiales_comunicacion_convocatoria.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 42.
LaTeX Font Info:    ... okay on input line 42.
LaTeX Info: Redefining \. on input line 42.
LaTeX Info: Redefining \% on input line 42.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=14.5pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mk
ii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count334
\scratchdimen=\dimen288
\scratchbox=\box125
\nofMPsegments=\count335
\nofMParguments=\count336
\everyMPshowfont=\toks40
\MPscratchCnt=\count337
\MPscratchDim=\dimen289
\MPnumerator=\count338
\makeMPintoPDFobject=\count339
\everyMPtoPDFconversion=\toks41
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-b
ase.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.c
fg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring ON on input line 42.
 (materiales_comunicacion_convocatoria.out)
(materiales_comunicacion_convocatoria.out)
\@outlinefile=\write4
\openout4 = `materiales_comunicacion_convocatoria.out'.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
(materiales_comunicacion_convocatoria.toc
LaTeX Info: Redefining \. on input line 1.
LaTeX Info: Redefining \% on input line 1.
LaTeX Font Info:    Trying to load font information for U+msa on input line 3.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 3.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
))
\tf@toc=\write5
\openout5 = `materiales_comunicacion_convocatoria.toc'.




pdfTeX warning (ext4): destination with the same identifier (name{page.1}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.66 \newpage
              [1]
LaTeX Font Info:    Trying to load font information for U+fontawesomefree1 on i
nput line 76.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\ufontaweso
mefree1.fd)
LaTeX Font Info:    Trying to load font information for U+fontawesomefree2 on i
nput line 109.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\ufontaweso
mefree2.fd)
LaTeX Font Info:    Trying to load font information for U+fontawesomefree0 on i
nput line 112.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\ufontaweso
mefree0.fd)
LaTeX Font Info:    Trying to load font information for U+fontawesomefree3 on i
nput line 126.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontawesome5\ufontaweso
mefree3.fd)

[2{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/fontawesome5/fa
5free1.enc}{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/fontaw
esome5/fa5free2.enc}{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvi
ps/fontawesome5/fa5free0.enc}]

[3{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/fontawesome5/fa
5free3.enc}]

[4]

[5]

[6]

[7]

[8]
LaTeX Font Info:    Font shape `OT1/cmtt/bx/n' in size <12> not available
(Font)              Font shape `OT1/cmtt/m/n' tried instead on input line 417.


[9]
Overfull \vbox (18.38625pt too high) has occurred while \output is active []



[10]

[11] (materiales_comunicacion_convocatoria.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
)
 ***********
LaTeX2e <2024-11-01>
L3 programming layer <2024-11-02>
 ***********
Package rerunfilecheck Info: File `materiales_comunicacion_convocatoria.out' ha
s not changed.
(rerunfilecheck)             Checksum: 51DF714606DD822F234EB10C895B13BC;4096.
 ) 
Here is how much of TeX's memory you used:
 27560 strings out of 473682
 541736 string characters out of 5719215
 904279 words of memory out of 5000000
 50193 multiletter control sequences out of 15000+600000
 566910 words of font info for 68 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 98i,12n,101p,478b,622s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/fontawesome5
/FontAwesome5Free-Solid.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts
/type1/public/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiK
TeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppData/Local/Prog
rams/MiKTeX/fonts/type1/public/amsfonts/cm/cmti12.pfb><C:/Users/<USER>/AppData/L
ocal/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmtt12.pfb><C:/Users/<USER>/
AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/symbols/msam10.pfb>
Output written on materiales_comunicacion_convocatoria.pdf (12 pages, 172238 by
tes).
PDF statistics:
 238 PDF objects out of 1000 (max. 8388607)
 45 named destinations out of 1000 (max. 500000)
 197 words of extra memory for PDF output out of 10000 (max. 10000000)

