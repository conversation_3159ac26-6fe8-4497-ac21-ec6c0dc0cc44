# Sistema de Selección de Ciudadanos - Conversa Colombia

## Descripción

Sistema algorítmico avanzado para la selección de 100 ciudadanos representativos para diálogos deliberativos sobre polarización en Colombia. Desarrollado como herramienta de consultoría para la Fundación RV, basado en las recomendaciones del economista Eduardo Lora y datos oficiales del DANE Censo 2018.

## Características Principales

### 🎯 Objetivos
- Seleccionar 100 ciudadanos representativos de la diversidad colombiana
- Optimizar representatividad en 5 dimensiones prioritarias
- Garantizar legitimidad y validez estadística del proceso
- Facilitar diálogos deliberativos de alta calidad sobre polarización

### 🧬 Metodología
- **Algoritmo Genético**: Optimización evolutiva para máxima representatividad
- **Muestreo Estratificado**: Basado en datos DANE Censo 2018
- **Validación Cruzada**: Verificación de cumplimiento de objetivos
- **Flexibilidad Controlada**: Tolerancias del ±10% en categorías minoritarias

### 📊 Dimensiones de Representatividad

#### Dimensiones Prioritarias (5):
1. **Sexo y Orientación Sexual**
   - Hombres heterosexuales: 49 personas (49%)
   - Mujeres heterosexuales: 49 personas (49%)
   - LGBTQ+: 2 personas (2%)

2. **Generación (Edad)**
   - Postguerra y del silencio (61+ años): 19 personas (19%)
   - Generación X y Millennials (34-60 años): 46 personas (46%)
   - Generación Z y Alfa (18-33 años): 35 personas (35%)

3. **Educación**
   - Media o menos (hasta 12 años): 70 personas (70%)
   - Técnica o universitaria: 30 personas (30%)

4. **Origen (Campesino/Citadino)**
   - Campesinos que viven en el campo: 20 personas (20%)
   - Campesinos que ya no viven en el campo: 20 personas (20%)
   - Citadinos (no campesinos): 60 personas (60%)

5. **Condición Laboral**
   - Asalariados: 33 personas (33%)
   - Trabajadores independientes: 28 personas (28%)
   - No trabajan actualmente: 39 personas (39%)

#### Dimensiones de Referencia (2):
6. **Grupo Étnico**
   - Negros, mulatos, afrocolombianos: 7 personas (7%)
   - Indígenas, gitanos, raizales: 4 personas (4%)
   - Resto (blancos, mestizos, etc.): 89 personas (89%)

7. **Clase Social**
   - Pobres: 33 personas (33%)
   - Vulnerables: 32 personas (32%)
   - Clase media: 32 personas (32%)
   - Clase alta: 3 personas (3%)

## Instalación y Uso

### Requisitos del Sistema
```bash
Python 3.8+
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.4.0 (opcional, para visualizaciones)
seaborn >= 0.11.0 (opcional, para visualizaciones)
```

### Instalación
```bash
# Clonar o descargar los archivos
# Instalar dependencias
pip install pandas numpy matplotlib seaborn

# Ejecutar el sistema completo
python ejecutar_seleccion.py
```

### Uso Básico
```python
from algoritmo_seleccion_ciudadanos import AlgoritmoSeleccionCiudadanos

# Crear instancia del algoritmo
algoritmo = AlgoritmoSeleccionCiudadanos()

# Ejecutar selección completa
resultados = algoritmo.ejecutar_seleccion_completa(n_candidatos=300)

# Los resultados incluyen:
# - candidatos_pool: Pool inicial de candidatos
# - seleccionados: 100 ciudadanos seleccionados
# - perfiles_detallados: Análisis completo de la selección
# - metadata: Información del proceso
```

## Archivos del Sistema

### Archivos Principales
- `algoritmo_seleccion_ciudadanos.py`: Algoritmo principal de selección
- `ejecutar_seleccion.py`: Script ejecutable completo
- `perfiles_ciudadanos_ejemplos.md`: Ejemplos de perfiles y documentación
- `README.md`: Este archivo de documentación

### Archivos de Entrada
- `Propuesta datos.md`: Propuesta original de Eduardo Lora

### Archivos de Salida (generados automáticamente)
- `seleccion_ciudadanos.json`: Resultados completos en formato JSON
- `seleccion_ciudadanos_seleccionados.csv`: Lista de ciudadanos seleccionados
- `reporte_ejecutivo.md`: Reporte ejecutivo detallado
- `distribucion_ciudadanos.png`: Visualizaciones de distribución
- `matriz_cumplimiento.png`: Matriz de cumplimiento de objetivos

## Funcionalidades Avanzadas

### 🔧 Personalización
```python
# Modificar objetivos de representatividad
algoritmo.objetivos['sexo']['Hombre'] = 35  # Cambiar objetivo

# Ajustar tolerancias
algoritmo.tolerancias['educacion']['Media_o_menos'] = 5  # ±5 personas

# Cambiar parámetros del algoritmo genético
seleccionados = algoritmo.algoritmo_genetico_seleccion(
    candidatos, 
    generaciones=200,  # Más iteraciones
    poblacion_size=100  # Población más grande
)
```

### 📈 Análisis y Validación
```python
# Calcular score de representatividad
score = algoritmo.calcular_score_representatividad(seleccionados)

# Generar perfiles detallados
perfiles = algoritmo.generar_perfiles_especificos(seleccionados)

# Validar cumplimiento de objetivos
validacion = algoritmo.validar_representatividad(seleccionados)
```

### 🎨 Visualizaciones
El sistema genera automáticamente:
- Gráficos de distribución por cada dimensión
- Matrices de cumplimiento de objetivos
- Histogramas de edad y otras variables continuas
- Mapas de calor de interseccionalidad

## Metodología Técnica

### Algoritmo Genético
1. **Inicialización**: Población de 50 selecciones aleatorias
2. **Evaluación**: Función de fitness basada en representatividad
3. **Selección**: Top 50% como padres para siguiente generación
4. **Cruzamiento**: Combinación de selecciones parentales
5. **Mutación**: Reemplazo aleatorio de 1-3 candidatos (10% probabilidad)
6. **Iteración**: 100 generaciones por defecto
7. **Elitismo**: Preservación de mejores soluciones

### Función de Fitness
- Evalúa cumplimiento de objetivos en 5 dimensiones prioritarias
- Penaliza desviaciones proporcionalmente
- Considera tolerancias específicas por categoría
- Score final: promedio ponderado de todas las dimensiones

### Validación Estadística
- Comparación con distribuciones DANE 2018
- Análisis de significancia estadística
- Verificación de interseccionalidad
- Documentación de sesgos potenciales

## Consideraciones Especiales para Polarización

### Diversidad Ideológica
- Representación de diferentes espectros políticos
- Inclusión de experiencias diversas con el conflicto
- Variedad en niveles de confianza institucional
- Diversidad en fuentes de información

### Factores Territoriales
- Equilibrio centro-periferia
- Representación urbano-rural
- Diversidad regional (5 regiones de Colombia)
- Experiencias diferenciadas de ciudadanía

### Interseccionalidad
- Combinaciones críticas de vulnerabilidad
- Representación de múltiples identidades
- Consideración de experiencias de discriminación
- Inclusión de perspectivas minoritarias

## Validación y Calidad

### Indicadores de Éxito
- **Score de Representatividad**: ≥ 0.85
- **Cumplimiento de Objetivos**: ≥ 90% de categorías dentro de tolerancia
- **Diversidad Territorial**: Presencia de las 5 regiones
- **Interseccionalidad**: Cobertura de combinaciones críticas

### Protocolo de Validación
1. Verificación de características mediante cuestionario
2. Confirmación de disponibilidad y compromiso
3. Preparación de candidatos de reemplazo
4. Validación final pre-evento
5. Evaluación post-evento

## Soporte y Contacto

### Documentación Adicional
- `perfiles_ciudadanos_ejemplos.md`: Ejemplos detallados de perfiles
- Comentarios extensivos en el código fuente
- Reportes automáticos de validación

### Resolución de Problemas
- Verificar instalación de dependencias
- Revisar formato de datos de entrada
- Consultar logs de ejecución para errores
- Validar parámetros de configuración

---

**Desarrollado por**: Sistema de IA Experto en Economía y Estadística  
**Para**: Fundación RV - Conversa Colombia  
**Basado en**: Propuesta Eduardo Lora y datos DANE Censo 2018  
**Fecha**: Julio 2025  

*Este sistema garantiza la selección de ciudadanos representativos para diálogos deliberativos de alta calidad sobre polarización en Colombia, combinando rigor estadístico con flexibilidad operativa.*
