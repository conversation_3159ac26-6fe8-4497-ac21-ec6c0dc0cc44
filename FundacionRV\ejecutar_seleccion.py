#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script Ejecutable para Selección de Ciudadanos
Conversa Colombia - Fundación RV

Este script ejecuta el algoritmo completo de selección y genera todos los reportes necesarios.
"""

import sys
import os
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from algoritmo_seleccion_ciudadanos import AlgoritmoSeleccionCiudadanos, exportar_resultados

def generar_visualizaciones(resultados, carpeta_salida="resultados"):
    """Genera visualizaciones de los resultados"""
    
    if not os.path.exists(carpeta_salida):
        os.makedirs(carpeta_salida)
    
    seleccionados = resultados['seleccionados']
    resumen = resultados['perfiles_detallados']['resumen_estadistico']
    
    # Configurar estilo
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # 1. Distribución por Sexo
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Distribución de Ciudadanos Seleccionados - Conversa Colombia', fontsize=16, fontweight='bold')
    
    # Sexo
    sexo_data = resumen['sexo']['conteos']
    axes[0,0].pie(sexo_data.values(), labels=sexo_data.keys(), autopct='%1.1f%%', startangle=90)
    axes[0,0].set_title('Distribución por Sexo')
    
    # Generación
    gen_data = resumen['generacion']['conteos']
    axes[0,1].bar(gen_data.keys(), gen_data.values(), color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    axes[0,1].set_title('Distribución por Generación')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Educación
    edu_data = resumen['educacion']['conteos']
    axes[0,2].pie(edu_data.values(), labels=edu_data.keys(), autopct='%1.1f%%', startangle=90)
    axes[0,2].set_title('Distribución por Educación')
    
    # Origen
    origen_data = resumen['origen']['conteos']
    axes[1,0].bar(origen_data.keys(), origen_data.values(), color=['#96CEB4', '#FFEAA7', '#DDA0DD'])
    axes[1,0].set_title('Distribución por Origen')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # Condición Laboral
    lab_data = resumen['condicion_laboral']['conteos']
    axes[1,1].pie(lab_data.values(), labels=lab_data.keys(), autopct='%1.1f%%', startangle=90)
    axes[1,1].set_title('Distribución por Condición Laboral')
    
    # Distribución de Edad
    edades = [p.edad for p in seleccionados]
    axes[1,2].hist(edades, bins=15, color='skyblue', alpha=0.7, edgecolor='black')
    axes[1,2].set_title('Distribución de Edades')
    axes[1,2].set_xlabel('Edad')
    axes[1,2].set_ylabel('Frecuencia')
    
    plt.tight_layout()
    plt.savefig(f'{carpeta_salida}/distribucion_ciudadanos.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Matriz de Cumplimiento de Objetivos
    fig, ax = plt.subplots(figsize=(12, 8))
    
    validacion = resultados['perfiles_detallados']['validacion_representatividad']
    cumplimiento_data = []
    
    for dimension, categorias in validacion['cumplimiento_objetivos'].items():
        for categoria, datos in categorias.items():
            cumplimiento_data.append({
                'Dimensión': dimension,
                'Categoría': categoria,
                'Objetivo': datos['objetivo'],
                'Actual': datos['actual'],
                'Cumple': 'Sí' if datos['cumple'] else 'No',
                'Desviación': datos['actual'] - datos['objetivo']
            })
    
    df_cumplimiento = pd.DataFrame(cumplimiento_data)
    
    # Crear heatmap de desviaciones
    pivot_data = df_cumplimiento.pivot_table(
        values='Desviación', 
        index='Dimensión', 
        columns='Categoría', 
        fill_value=0
    )
    
    sns.heatmap(pivot_data, annot=True, cmap='RdYlGn_r', center=0, 
                fmt='.0f', cbar_kws={'label': 'Desviación del Objetivo'})
    plt.title('Matriz de Cumplimiento de Objetivos\n(Verde: Dentro del objetivo, Rojo: Fuera del objetivo)')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(f'{carpeta_salida}/matriz_cumplimiento.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Visualizaciones guardadas en carpeta '{carpeta_salida}'")

def generar_reporte_ejecutivo(resultados, carpeta_salida="resultados"):
    """Genera reporte ejecutivo en formato texto"""
    
    if not os.path.exists(carpeta_salida):
        os.makedirs(carpeta_salida)
    
    validacion = resultados['perfiles_detallados']['validacion_representatividad']
    resumen = resultados['perfiles_detallados']['resumen_estadistico']
    metadata = resultados['metadata']
    
    reporte = f"""
# REPORTE EJECUTIVO - SELECCIÓN DE CIUDADANOS
## Conversa Colombia - Diálogos Deliberativos sobre Polarización

**Fecha de Selección:** {metadata['fecha_seleccion']}
**Candidatos Evaluados:** {metadata['n_candidatos_inicial']}
**Ciudadanos Seleccionados:** {metadata['n_seleccionados']}
**Score de Representatividad:** {metadata['score_representatividad']:.3f}/1.000

---

## RESUMEN EJECUTIVO

La selección algorítmica ha identificado 100 ciudadanos que representan de manera óptima la diversidad demográfica, socioeconómica y territorial de Colombia para participar en diálogos deliberativos sobre polarización.

### INDICADORES CLAVE DE REPRESENTATIVIDAD

**✅ DIMENSIONES QUE CUMPLEN OBJETIVOS:**
"""
    
    # Analizar cumplimiento por dimensión
    dimensiones_ok = []
    dimensiones_alerta = []
    
    for dimension, categorias in validacion['cumplimiento_objetivos'].items():
        cumple_todas = all(cat['cumple'] for cat in categorias.values())
        if cumple_todas:
            dimensiones_ok.append(dimension)
        else:
            dimensiones_alerta.append(dimension)
    
    for dim in dimensiones_ok:
        reporte += f"- {dim.upper()}\n"
    
    reporte += f"\n**⚠️ DIMENSIONES CON ALERTAS:** {len(dimensiones_alerta)}\n"
    for dim in dimensiones_alerta:
        reporte += f"- {dim.upper()}\n"
    
    reporte += f"""

### COMPOSICIÓN FINAL SELECCIONADA

**DISTRIBUCIÓN POR SEXO:**
"""
    for categoria, datos in resumen['sexo']['conteos'].items():
        porcentaje = resumen['sexo']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""
**DISTRIBUCIÓN POR GENERACIÓN:**
"""
    for categoria, datos in resumen['generacion']['conteos'].items():
        porcentaje = resumen['generacion']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""
**DISTRIBUCIÓN POR EDUCACIÓN:**
"""
    for categoria, datos in resumen['educacion']['conteos'].items():
        porcentaje = resumen['educacion']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""
**DISTRIBUCIÓN POR ORIGEN:**
"""
    for categoria, datos in resumen['origen']['conteos'].items():
        porcentaje = resumen['origen']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""
**DISTRIBUCIÓN POR CONDICIÓN LABORAL:**
"""
    for categoria, datos in resumen['condicion_laboral']['conteos'].items():
        porcentaje = resumen['condicion_laboral']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""

### ESTADÍSTICAS DE EDAD
- **Edad Promedio:** {resumen['estadisticas_edad']['promedio']} años
- **Edad Mediana:** {resumen['estadisticas_edad']['mediana']} años
- **Rango de Edad:** {resumen['estadisticas_edad']['minima']} - {resumen['estadisticas_edad']['maxima']} años
- **Desviación Estándar:** {resumen['estadisticas_edad']['desviacion_estandar']} años

### DIVERSIDAD ÉTNICA (REFERENCIA)
"""
    for categoria, datos in resumen['grupo_etnico']['conteos'].items():
        porcentaje = resumen['grupo_etnico']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    reporte += f"""
### DISTRIBUCIÓN SOCIOECONÓMICA (REFERENCIA)
"""
    for categoria, datos in resumen['clase_social']['conteos'].items():
        porcentaje = resumen['clase_social']['porcentajes'][categoria]
        reporte += f"- {categoria}: {datos} personas ({porcentaje}%)\n"
    
    if validacion['alertas']:
        reporte += f"""

### ALERTAS Y RECOMENDACIONES

**DESVIACIONES DETECTADAS:**
"""
        for alerta in validacion['alertas']:
            reporte += f"- {alerta}\n"
        
        reporte += f"""
**RECOMENDACIONES:**
- Revisar disponibilidad de candidatos en categorías con déficit
- Considerar flexibilidad en categorías minoritarias (LGBTQ+, Clase Alta)
- Validar representatividad territorial en selección final
- Preparar candidatos de reemplazo para categorías críticas
"""
    
    reporte += f"""

### PRÓXIMOS PASOS

1. **Validación Final:** Confirmar disponibilidad y características de seleccionados
2. **Logística:** Coordinar transporte, alojamiento y alimentación
3. **Preparación:** Enviar materiales informativos y agenda
4. **Contingencia:** Activar reemplazos si hay cancelaciones
5. **Seguimiento:** Implementar protocolo de evaluación post-evento

---

**Metodología:** Algoritmo genético de optimización con 5 dimensiones prioritarias
**Fuente de Datos:** DANE Censo 2018 y propuesta Eduardo Lora
**Herramienta:** Sistema de IA especializado en muestreo representativo

*Reporte generado automáticamente por el Sistema de Selección de Ciudadanos*
"""
    
    # Guardar reporte
    with open(f'{carpeta_salida}/reporte_ejecutivo.md', 'w', encoding='utf-8') as f:
        f.write(reporte)
    
    print(f"✅ Reporte ejecutivo guardado: {carpeta_salida}/reporte_ejecutivo.md")

def main():
    """Función principal que ejecuta todo el proceso"""
    
    print("🇨🇴 SISTEMA DE SELECCIÓN DE CIUDADANOS - CONVERSA COLOMBIA")
    print("=" * 70)
    print("Algoritmo de optimización para diálogos deliberativos sobre polarización")
    print("Basado en metodología Eduardo Lora y datos DANE 2018")
    print("=" * 70)
    
    # Crear carpeta de resultados con timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    carpeta_salida = f"resultados_{timestamp}"
    
    try:
        # Ejecutar algoritmo
        algoritmo = AlgoritmoSeleccionCiudadanos()
        resultados = algoritmo.ejecutar_seleccion_completa(n_candidatos=300)
        
        # Crear carpeta de salida
        os.makedirs(carpeta_salida, exist_ok=True)
        
        # Exportar resultados básicos
        exportar_resultados(resultados, f"{carpeta_salida}/seleccion_ciudadanos")
        
        # Generar visualizaciones
        print("\n📊 Generando visualizaciones...")
        generar_visualizaciones(resultados, carpeta_salida)
        
        # Generar reporte ejecutivo
        print("📋 Generando reporte ejecutivo...")
        generar_reporte_ejecutivo(resultados, carpeta_salida)
        
        # Mostrar resumen final
        print(f"\n🎉 PROCESO COMPLETADO EXITOSAMENTE")
        print(f"📁 Todos los archivos guardados en: {carpeta_salida}/")
        print(f"📈 Score de Representatividad: {resultados['metadata']['score_representatividad']:.3f}")
        
        # Listar archivos generados
        print(f"\n📄 ARCHIVOS GENERADOS:")
        for archivo in os.listdir(carpeta_salida):
            print(f"   • {archivo}")
        
        return resultados
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return None

if __name__ == "__main__":
    # Verificar dependencias
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("⚠️  ADVERTENCIA: matplotlib y seaborn no están instalados.")
        print("   Las visualizaciones no se generarán.")
        print("   Instalar con: pip install matplotlib seaborn")
    
    # Ejecutar proceso principal
    resultados = main()
    
    if resultados:
        print(f"\n✅ Proceso completado. Los 100 ciudadanos han sido seleccionados exitosamente.")
        print(f"🚀 ¡Listos para los diálogos deliberativos sobre polarización en Colombia!")
