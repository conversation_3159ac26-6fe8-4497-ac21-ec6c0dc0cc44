#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Predictor de Clase Social basado en Educación + Factores Complementarios
Fundación RV - Conversa Colombia
Basado en metodología DANE y análisis estadístico
"""

import random
import pandas as pd
from typing import Dict, Tuple

class PredictorClaseSocial:
    """Predice clase social basado en educación y factores complementarios"""
    
    def __init__(self):
        # Probabilidades de clase social por nivel educativo (basado en DANE)
        self.prob_educacion = {
            'Sin_educacion': {
                'Pobre': 0.70, 'Vulnerable': 0.25, 'Media': 0.05, 'Alta': 0.00
            },
            'Primaria': {
                'Pobre': 0.55, 'Vulnerable': 0.35, 'Media': 0.10, 'Alta': 0.00
            },
            'Secundaria': {
                'Pobre': 0.35, 'Vulnerable': 0.40, 'Media': 0.24, 'Alta': 0.01
            },
            'Tecnica': {
                'Pobre': 0.20, 'Vulnerable': 0.35, 'Media': 0.42, 'Alta': 0.03
            },
            'Universitaria': {
                'Pobre': 0.10, 'Vulnerable': 0.25, 'Media': 0.55, 'Alta': 0.10
            },
            'Posgrado': {
                'Pobre': 0.05, 'Vulnerable': 0.15, 'Media': 0.50, 'Alta': 0.30
            }
        }
        
        # Factores de ajuste por condición laboral
        self.ajuste_laboral = {
            'Asalariado': {'Pobre': 0.8, 'Vulnerable': 1.0, 'Media': 1.2, 'Alta': 1.1},
            'Independiente': {'Pobre': 1.1, 'Vulnerable': 1.1, 'Media': 1.0, 'Alta': 1.3},
            'No_trabaja': {'Pobre': 1.3, 'Vulnerable': 1.2, 'Media': 0.8, 'Alta': 0.9}
        }
        
        # Factores de ajuste por zona
        self.ajuste_zona = {
            'Urbana': {'Pobre': 0.9, 'Vulnerable': 1.0, 'Media': 1.1, 'Alta': 1.2},
            'Rural': {'Pobre': 1.2, 'Vulnerable': 1.1, 'Media': 0.8, 'Alta': 0.6}
        }
        
        # Factores de ajuste por origen
        self.ajuste_origen = {
            'Citadino': {'Pobre': 0.8, 'Vulnerable': 0.9, 'Media': 1.2, 'Alta': 1.3},
            'Campesino_campo': {'Pobre': 1.4, 'Vulnerable': 1.2, 'Media': 0.7, 'Alta': 0.4},
            'Campesino_ciudad': {'Pobre': 1.2, 'Vulnerable': 1.1, 'Media': 0.9, 'Alta': 0.6}
        }
        
        # Factores de ajuste por grupo étnico
        self.ajuste_etnico = {
            'Resto': {'Pobre': 1.0, 'Vulnerable': 1.0, 'Media': 1.0, 'Alta': 1.0},
            'Afrocolombiano': {'Pobre': 1.3, 'Vulnerable': 1.2, 'Media': 0.8, 'Alta': 0.5},
            'Indigena': {'Pobre': 1.5, 'Vulnerable': 1.1, 'Media': 0.6, 'Alta': 0.3}
        }
    
    def mapear_educacion(self, educacion: str) -> str:
        """Mapea la educación del algoritmo a categorías detalladas"""
        if educacion == 'Media_o_menos':
            # Subdividir aleatoriamente
            opciones = ['Sin_educacion', 'Primaria', 'Secundaria']
            pesos = [0.15, 0.35, 0.50]  # Distribución realista
            return random.choices(opciones, weights=pesos)[0]
        elif educacion == 'Tecnica_universitaria':
            # Subdividir entre técnica y universitaria
            opciones = ['Tecnica', 'Universitaria', 'Posgrado']
            pesos = [0.60, 0.35, 0.05]  # Distribución realista
            return random.choices(opciones, weights=pesos)[0]
        else:
            return 'Secundaria'  # Default
    
    def predecir_clase_social(self, educacion: str, condicion_laboral: str, 
                             zona: str, origen: str, grupo_etnico: str) -> str:
        """Predice la clase social basada en múltiples factores"""
        
        # Mapear educación a categoría detallada
        edu_detallada = self.mapear_educacion(educacion)
        
        # Obtener probabilidades base por educación
        prob_base = self.prob_educacion[edu_detallada].copy()
        
        # Aplicar ajustes por factores complementarios
        for clase in prob_base.keys():
            prob_base[clase] *= self.ajuste_laboral[condicion_laboral][clase]
            prob_base[clase] *= self.ajuste_zona[zona][clase]
            prob_base[clase] *= self.ajuste_origen[origen][clase]
            prob_base[clase] *= self.ajuste_etnico[grupo_etnico][clase]
        
        # Normalizar probabilidades
        total = sum(prob_base.values())
        prob_normalizada = {k: v/total for k, v in prob_base.items()}
        
        # Seleccionar clase social basada en probabilidades
        clases = list(prob_normalizada.keys())
        pesos = list(prob_normalizada.values())
        
        return random.choices(clases, weights=pesos)[0]
    
    def generar_matriz_correlacion(self) -> pd.DataFrame:
        """Genera matriz de correlación educación-clase social"""
        
        datos = []
        
        # Simular 10,000 casos
        for _ in range(10000):
            # Generar perfil aleatorio
            educacion = random.choice(['Media_o_menos', 'Tecnica_universitaria'])
            condicion_laboral = random.choice(['Asalariado', 'Independiente', 'No_trabaja'])
            zona = random.choice(['Urbana', 'Rural'])
            origen = random.choice(['Citadino', 'Campesino_campo', 'Campesino_ciudad'])
            grupo_etnico = random.choice(['Resto', 'Afrocolombiano', 'Indigena'])
            
            # Predecir clase social
            clase_social = self.predecir_clase_social(
                educacion, condicion_laboral, zona, origen, grupo_etnico
            )
            
            datos.append({
                'educacion': educacion,
                'condicion_laboral': condicion_laboral,
                'zona': zona,
                'origen': origen,
                'grupo_etnico': grupo_etnico,
                'clase_social': clase_social
            })
        
        return pd.DataFrame(datos)
    
    def analizar_correlaciones(self) -> Dict:
        """Analiza las correlaciones entre variables"""
        
        df = self.generar_matriz_correlacion()
        
        # Tabla cruzada educación x clase social
        tabla_edu_clase = pd.crosstab(df['educacion'], df['clase_social'], normalize='index')
        
        # Tabla cruzada condición laboral x clase social
        tabla_lab_clase = pd.crosstab(df['condicion_laboral'], df['clase_social'], normalize='index')
        
        # Tabla cruzada origen x clase social
        tabla_origen_clase = pd.crosstab(df['origen'], df['clase_social'], normalize='index')
        
        return {
            'educacion_x_clase': tabla_edu_clase,
            'laboral_x_clase': tabla_lab_clase,
            'origen_x_clase': tabla_origen_clase,
            'datos_completos': df
        }

def mostrar_analisis():
    """Muestra el análisis de correlaciones"""
    
    predictor = PredictorClaseSocial()
    analisis = predictor.analizar_correlaciones()
    
    print("📊 ANÁLISIS DE CORRELACIÓN: EDUCACIÓN + FACTORES → CLASE SOCIAL")
    print("=" * 70)
    
    print("\n🎓 EDUCACIÓN x CLASE SOCIAL (% por fila):")
    print(analisis['educacion_x_clase'].round(3))
    
    print("\n💼 CONDICIÓN LABORAL x CLASE SOCIAL (% por fila):")
    print(analisis['laboral_x_clase'].round(3))
    
    print("\n🌍 ORIGEN x CLASE SOCIAL (% por fila):")
    print(analisis['origen_x_clase'].round(3))
    
    # Calcular correlación general
    df = analisis['datos_completos']
    
    # Convertir a numérico para correlación
    df_num = df.copy()
    df_num['educacion_num'] = df_num['educacion'].map({'Media_o_menos': 0, 'Tecnica_universitaria': 1})
    df_num['clase_num'] = df_num['clase_social'].map({'Pobre': 0, 'Vulnerable': 1, 'Media': 2, 'Alta': 3})
    
    correlacion = df_num['educacion_num'].corr(df_num['clase_num'])
    
    print(f"\n📈 CORRELACIÓN EDUCACIÓN-CLASE SOCIAL: {correlacion:.3f}")
    
    if correlacion > 0.6:
        print("✅ CORRELACIÓN FUERTE: La educación es un buen predictor de clase social")
    elif correlacion > 0.4:
        print("⚠️ CORRELACIÓN MODERADA: La educación es un predictor aceptable")
    else:
        print("❌ CORRELACIÓN DÉBIL: La educación no es suficiente para predecir clase")
    
    return analisis

if __name__ == "__main__":
    mostrar_analisis()
