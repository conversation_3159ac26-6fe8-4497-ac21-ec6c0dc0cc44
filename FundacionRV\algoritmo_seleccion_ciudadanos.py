#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Algoritmo de Selección de Ciudadanos para Diálogos Deliberativos
Fundación RV - Conversa Colombia
Autor: Sistema de IA Experto
Fecha: Julio 2025

Este algoritmo selecciona 100 ciudadanos representativos para diálogos deliberativos
sobre polarización en Colombia, basado en las recomendaciones de Eduardo Lora
y datos del DANE Censo 2018.
"""

import pandas as pd
import numpy as np
import random
from typing import Dict, List, Tuple
from dataclasses import dataclass
from itertools import product
import json

@dataclass
class PerfilCiudadano:
    """Clase para representar el perfil de un ciudadano"""
    id: int
    sexo: str  # 'Hombre', 'Mujer', 'LGBTQ+'
    edad: int
    generacion: str  # 'Postguerra', 'X_Millennials', 'Z_Alfa'
    educacion: str  # 'Media_o_menos', 'Tecnica_universitaria'
    origen: str  # 'Campesino_campo', 'Campesino_ciudad', 'Citadino'
    condicion_laboral: str  # 'Asalariado', 'Independiente', 'No_trabaja'
    grupo_etnico: str  # 'Afrocolombiano', 'Indigena', 'Resto'
    clase_social: str  # 'Pobre', 'Vulnerable', 'Media', 'Alta'
    region: str  # 'Caribe', 'Andina', 'Pacifica', 'Orinoquia', 'Amazonia'
    zona: str  # 'Urbana', 'Rural'
    score_representatividad: float = 0.0

class AlgoritmoSeleccionCiudadanos:
    """Algoritmo principal para selección de 100 ciudadanos representativos"""
    
    def __init__(self):
        # Objetivos para 100 personas basados en propuesta Eduardo Lora
        self.objetivos = {
            'sexo': {
                'Hombre': 49,
                'Mujer': 49,
                'LGBTQ+': 2
            },
            'generacion': {
                'Postguerra': 19,  # 61+ años
                'X_Millennials': 46,  # 34-60 años
                'Z_Alfa': 35  # 18-33 años
            },
            'educacion': {
                'Media_o_menos': 70,  # 70%
                'Tecnica_universitaria': 30  # 30%
            },
            'origen': {
                'Campesino_campo': 20,  # 20%
                'Campesino_ciudad': 20,  # 20%
                'Citadino': 60  # 60%
            },
            'condicion_laboral': {
                'Asalariado': 33,  # 33%
                'Independiente': 28,  # 28%
                'No_trabaja': 39  # 39%
            }
        }

        # Objetivos de referencia (para validación)
        self.objetivos_referencia = {
            'grupo_etnico': {
                'Afrocolombiano': 7,  # 7%
                'Indigena': 4,  # 4%
                'Resto': 89  # 89%
            },
            'clase_social': {
                'Pobre': 33,  # 33%
                'Vulnerable': 32,  # 32%
                'Media': 32,  # 32%
                'Alta': 3  # 3%
            }
        }
        
        # Tolerancias (±10% del valor objetivo)
        self.tolerancias = {dim: {cat: max(1, int(obj * 0.1)) 
                                 for cat, obj in cats.items()} 
                           for dim, cats in self.objetivos.items()}
    
    def generar_pool_candidatos(self, n_candidatos: int = 200) -> List[PerfilCiudadano]:
        """Genera un pool de candidatos con distribución realista"""
        candidatos = []
        
        # Distribuciones basadas en DANE 2018
        distribuciones = {
            'sexo': ['Hombre'] * 48 + ['Mujer'] * 50 + ['LGBTQ+'] * 2,
            'edad_ranges': [(18, 33)] * 35 + [(34, 60)] * 46 + [(61, 85)] * 19,
            'educacion': ['Media_o_menos'] * 70 + ['Tecnica_universitaria'] * 30,
            'origen': ['Campesino_campo'] * 20 + ['Campesino_ciudad'] * 20 + ['Citadino'] * 60,
            'condicion_laboral': ['Asalariado'] * 33 + ['Independiente'] * 28 + ['No_trabaja'] * 39,
            'grupo_etnico': ['Afrocolombiano'] * 7 + ['Indigena'] * 4 + ['Resto'] * 89,
            'clase_social': ['Pobre'] * 33 + ['Vulnerable'] * 32 + ['Media'] * 32 + ['Alta'] * 3,
            'region': ['Andina'] * 40 + ['Caribe'] * 25 + ['Pacifica'] * 15 + ['Orinoquia'] * 12 + ['Amazonia'] * 8,
            'zona': ['Urbana'] * 77 + ['Rural'] * 23
        }
        
        for i in range(n_candidatos):
            # Selección aleatoria con distribuciones realistas
            sexo = random.choice(distribuciones['sexo'])
            edad_range = random.choice(distribuciones['edad_ranges'])
            edad = random.randint(edad_range[0], edad_range[1])
            
            # Determinar generación basada en edad
            if edad >= 61:
                generacion = 'Postguerra'
            elif edad >= 34:
                generacion = 'X_Millennials'
            else:
                generacion = 'Z_Alfa'
            
            candidato = PerfilCiudadano(
                id=i + 1,
                sexo=sexo,
                edad=edad,
                generacion=generacion,
                educacion=random.choice(distribuciones['educacion']),
                origen=random.choice(distribuciones['origen']),
                condicion_laboral=random.choice(distribuciones['condicion_laboral']),
                grupo_etnico=random.choice(distribuciones['grupo_etnico']),
                clase_social=random.choice(distribuciones['clase_social']),
                region=random.choice(distribuciones['region']),
                zona=random.choice(distribuciones['zona'])
            )
            
            candidatos.append(candidato)
        
        return candidatos
    
    def calcular_score_representatividad(self, seleccionados: List[PerfilCiudadano]) -> float:
        """Calcula qué tan representativa es una selección"""
        score_total = 0
        dimensiones_evaluadas = 0
        
        # Evaluar dimensiones prioritarias
        for dimension, objetivos_dim in self.objetivos.items():
            conteos = {}
            for categoria in objetivos_dim.keys():
                conteos[categoria] = 0
            
            # Contar seleccionados por categoría
            for persona in seleccionados:
                valor = getattr(persona, dimension)
                if valor in conteos:
                    conteos[valor] += 1
            
            # Calcular score para esta dimensión
            score_dimension = 0
            for categoria, objetivo in objetivos_dim.items():
                actual = conteos[categoria]
                tolerancia = self.tolerancias[dimension][categoria]
                
                if abs(actual - objetivo) <= tolerancia:
                    score_dimension += 1.0
                else:
                    # Penalizar proporcionalmente a la desviación
                    desviacion = abs(actual - objetivo)
                    penalizacion = min(1.0, desviacion / objetivo) if objetivo > 0 else 1.0
                    score_dimension += max(0, 1.0 - penalizacion)
            
            score_total += score_dimension / len(objetivos_dim)
            dimensiones_evaluadas += 1
        
        return score_total / dimensiones_evaluadas if dimensiones_evaluadas > 0 else 0
    
    def algoritmo_genetico_seleccion(self, candidatos: List[PerfilCiudadano], 
                                   generaciones: int = 100, 
                                   poblacion_size: int = 50) -> List[PerfilCiudadano]:
        """Algoritmo genético para optimizar la selección"""
        
        def crear_individuo():
            """Crea una selección aleatoria de 100 candidatos"""
            return random.sample(candidatos, 100)
        
        def fitness(individuo):
            """Función de fitness basada en representatividad"""
            return self.calcular_score_representatividad(individuo)
        
        def cruzar(padre1, padre2):
            """Cruza dos selecciones para crear una nueva"""
            # Tomar 50 de cada padre, evitando duplicados
            hijo = list(padre1[:50])
            for candidato in padre2:
                if candidato not in hijo and len(hijo) < 100:
                    hijo.append(candidato)

            # Completar con candidatos aleatorios si es necesario
            while len(hijo) < 100:
                candidato = random.choice(candidatos)
                if candidato not in hijo:
                    hijo.append(candidato)

            return hijo[:100]
        
        def mutar(individuo, tasa_mutacion=0.1):
            """Muta una selección reemplazando algunos candidatos"""
            if random.random() < tasa_mutacion:
                # Reemplazar 1-3 candidatos aleatoriamente
                n_reemplazos = random.randint(1, 3)
                for _ in range(n_reemplazos):
                    if len(individuo) > 0:
                        idx_remover = random.randint(0, len(individuo) - 1)
                        candidato_nuevo = random.choice(candidatos)
                        if candidato_nuevo not in individuo:
                            individuo[idx_remover] = candidato_nuevo
            return individuo
        
        # Inicializar población
        poblacion = [crear_individuo() for _ in range(poblacion_size)]
        
        mejor_fitness = 0
        mejor_individuo = None
        
        for generacion in range(generaciones):
            # Evaluar fitness
            fitness_scores = [(individuo, fitness(individuo)) for individuo in poblacion]
            fitness_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Actualizar mejor solución
            if fitness_scores[0][1] > mejor_fitness:
                mejor_fitness = fitness_scores[0][1]
                mejor_individuo = fitness_scores[0][0].copy()
            
            # Selección de padres (top 50%)
            padres = [ind for ind, _ in fitness_scores[:poblacion_size//2]]
            
            # Crear nueva generación
            nueva_poblacion = padres.copy()  # Elitismo
            
            while len(nueva_poblacion) < poblacion_size:
                padre1 = random.choice(padres)
                padre2 = random.choice(padres)
                hijo = cruzar(padre1, padre2)
                hijo = mutar(hijo)
                nueva_poblacion.append(hijo)
            
            poblacion = nueva_poblacion
            
            # Mostrar progreso cada 20 generaciones
            if generacion % 20 == 0:
                print(f"Generación {generacion}: Mejor fitness = {mejor_fitness:.3f}")
        
        return mejor_individuo

    def generar_perfiles_especificos(self, seleccionados: List[PerfilCiudadano]) -> Dict:
        """Genera perfiles detallados de los ciudadanos seleccionados"""
        perfiles = {
            'resumen_estadistico': self.generar_resumen_estadistico(seleccionados),
            'perfiles_individuales': [],
            'matriz_cruzada': self.generar_matriz_cruzada(seleccionados),
            'validacion_representatividad': self.validar_representatividad(seleccionados)
        }

        # Generar perfiles individuales detallados
        for i, persona in enumerate(seleccionados, 1):
            perfil_detallado = {
                'numero_participante': i,
                'id_original': persona.id,
                'caracteristicas_demograficas': {
                    'sexo': persona.sexo,
                    'edad': persona.edad,
                    'generacion': persona.generacion,
                    'grupo_etnico': persona.grupo_etnico
                },
                'caracteristicas_socioeconomicas': {
                    'educacion': persona.educacion,
                    'condicion_laboral': persona.condicion_laboral,
                    'clase_social': persona.clase_social
                },
                'caracteristicas_territoriales': {
                    'origen': persona.origen,
                    'region': persona.region,
                    'zona': persona.zona
                },
                'perfil_narrativo': self.generar_narrativa_perfil(persona)
            }
            perfiles['perfiles_individuales'].append(perfil_detallado)

        return perfiles

    def generar_narrativa_perfil(self, persona: PerfilCiudadano) -> str:
        """Genera una narrativa descriptiva del perfil del ciudadano"""

        # Mapeo de características a descripciones
        descripciones = {
            'sexo': {
                'Hombre': 'hombre heterosexual',
                'Mujer': 'mujer heterosexual',
                'LGBTQ+': 'persona de la comunidad LGBTQ+'
            },
            'generacion': {
                'Postguerra': 'de la generación postguerra y del silencio',
                'X_Millennials': 'de la generación X o millennial',
                'Z_Alfa': 'de la generación Z o alfa'
            },
            'educacion': {
                'Media_o_menos': 'con educación media o menos',
                'Tecnica_universitaria': 'con educación técnica o universitaria'
            },
            'origen': {
                'Campesino_campo': 'campesino que vive en el campo',
                'Campesino_ciudad': 'campesino que ya no vive en el campo',
                'Citadino': 'citadino (no campesino)'
            },
            'condicion_laboral': {
                'Asalariado': 'trabajador asalariado',
                'Independiente': 'trabajador independiente',
                'No_trabaja': 'actualmente no trabaja'
            },
            'grupo_etnico': {
                'Afrocolombiano': 'afrocolombiano',
                'Indigena': 'indígena',
                'Resto': 'mestizo/blanco'
            },
            'clase_social': {
                'Pobre': 'de clase social pobre',
                'Vulnerable': 'de clase social vulnerable',
                'Media': 'de clase media',
                'Alta': 'de clase alta'
            }
        }

        narrativa = f"Participante #{persona.id}: {descripciones['sexo'][persona.sexo]} "
        narrativa += f"de {persona.edad} años, {descripciones['generacion'][persona.generacion]}, "
        narrativa += f"{descripciones['educacion'][persona.educacion]}, "
        narrativa += f"{descripciones['origen'][persona.origen]}, "
        narrativa += f"{descripciones['condicion_laboral'][persona.condicion_laboral]}, "
        narrativa += f"{descripciones['grupo_etnico'][persona.grupo_etnico]} "
        narrativa += f"y {descripciones['clase_social'][persona.clase_social]}. "
        narrativa += f"Proviene de la región {persona.region} en zona {persona.zona.lower()}."

        return narrativa

    def generar_resumen_estadistico(self, seleccionados: List[PerfilCiudadano]) -> Dict:
        """Genera resumen estadístico de la selección"""
        resumen = {}

        # Contar por cada dimensión
        dimensiones = ['sexo', 'generacion', 'educacion', 'origen', 'condicion_laboral',
                      'grupo_etnico', 'clase_social', 'region', 'zona']

        for dimension in dimensiones:
            conteos = {}
            for persona in seleccionados:
                valor = getattr(persona, dimension)
                conteos[valor] = conteos.get(valor, 0) + 1

            # Calcular porcentajes
            porcentajes = {k: round((v/100)*100, 1) for k, v in conteos.items()}

            resumen[dimension] = {
                'conteos': conteos,
                'porcentajes': porcentajes
            }

        # Estadísticas de edad
        edades = [p.edad for p in seleccionados]
        resumen['estadisticas_edad'] = {
            'promedio': round(np.mean(edades), 1),
            'mediana': round(np.median(edades), 1),
            'minima': min(edades),
            'maxima': max(edades),
            'desviacion_estandar': round(np.std(edades), 1)
        }

        return resumen

    def generar_matriz_cruzada(self, seleccionados: List[PerfilCiudadano]) -> Dict:
        """Genera matrices cruzadas de las principales dimensiones"""
        matrices = {}

        # Matriz Sexo x Generación
        matriz_sexo_gen = {}
        for persona in seleccionados:
            key = f"{persona.sexo}_{persona.generacion}"
            matriz_sexo_gen[key] = matriz_sexo_gen.get(key, 0) + 1

        matrices['sexo_x_generacion'] = matriz_sexo_gen

        # Matriz Educación x Origen
        matriz_edu_origen = {}
        for persona in seleccionados:
            key = f"{persona.educacion}_{persona.origen}"
            matriz_edu_origen[key] = matriz_edu_origen.get(key, 0) + 1

        matrices['educacion_x_origen'] = matriz_edu_origen

        # Matriz Condición Laboral x Clase Social
        matriz_lab_clase = {}
        for persona in seleccionados:
            key = f"{persona.condicion_laboral}_{persona.clase_social}"
            matriz_lab_clase[key] = matriz_lab_clase.get(key, 0) + 1

        matrices['laboral_x_clase'] = matriz_lab_clase

        return matrices

    def validar_representatividad(self, seleccionados: List[PerfilCiudadano]) -> Dict:
        """Valida qué tan representativa es la selección final"""
        validacion = {
            'score_general': self.calcular_score_representatividad(seleccionados),
            'cumplimiento_objetivos': {},
            'desviaciones': {},
            'alertas': []
        }

        # Validar cada dimensión prioritaria
        for dimension, objetivos_dim in self.objetivos.items():
            conteos = {}
            for categoria in objetivos_dim.keys():
                conteos[categoria] = 0

            for persona in seleccionados:
                valor = getattr(persona, dimension)
                if valor in conteos:
                    conteos[valor] += 1

            cumplimiento = {}
            desviaciones = {}

            for categoria, objetivo in objetivos_dim.items():
                actual = conteos[categoria]
                tolerancia = self.tolerancias[dimension][categoria]

                cumple = abs(actual - objetivo) <= tolerancia
                desviacion = actual - objetivo

                cumplimiento[categoria] = {
                    'objetivo': objetivo,
                    'actual': actual,
                    'cumple': cumple,
                    'tolerancia': tolerancia
                }

                desviaciones[categoria] = desviacion

                # Generar alertas para desviaciones significativas
                if not cumple:
                    validacion['alertas'].append(
                        f"ALERTA: {dimension}.{categoria} - Objetivo: {objetivo}, "
                        f"Actual: {actual}, Desviación: {desviacion}"
                    )

            validacion['cumplimiento_objetivos'][dimension] = cumplimiento
            validacion['desviaciones'][dimension] = desviaciones

        return validacion

    def ejecutar_seleccion_completa(self, n_candidatos: int = 200) -> Dict:
        """Ejecuta el proceso completo de selección"""
        print("🚀 Iniciando proceso de selección de 100 ciudadanos...")
        print(f"📊 Generando pool de {n_candidatos} candidatos...")

        # Generar candidatos
        candidatos = self.generar_pool_candidatos(n_candidatos)
        print(f"✅ Pool de candidatos generado: {len(candidatos)} personas")

        # Ejecutar algoritmo de selección
        print("🧬 Ejecutando algoritmo genético de optimización...")
        seleccionados = self.algoritmo_genetico_seleccion(candidatos)
        print(f"✅ Selección optimizada completada: {len(seleccionados)} personas")

        # Generar perfiles detallados
        print("📋 Generando perfiles detallados...")
        perfiles = self.generar_perfiles_especificos(seleccionados)
        print("✅ Perfiles generados exitosamente")

        # Mostrar resumen de validación
        validacion = perfiles['validacion_representatividad']
        print(f"\n📈 SCORE DE REPRESENTATIVIDAD: {validacion['score_general']:.3f}")
        print(f"⚠️  ALERTAS: {len(validacion['alertas'])}")

        if validacion['alertas']:
            print("\n🔍 ALERTAS DETECTADAS:")
            for alerta in validacion['alertas']:
                print(f"   • {alerta}")

        return {
            'candidatos_pool': candidatos,
            'seleccionados': seleccionados,
            'perfiles_detallados': perfiles,
            'metadata': {
                'fecha_seleccion': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
                'n_candidatos_inicial': len(candidatos),
                'n_seleccionados': len(seleccionados),
                'score_representatividad': validacion['score_general']
            }
        }

def exportar_resultados(resultados: Dict, archivo_base: str = "seleccion_ciudadanos"):
    """Exporta los resultados a diferentes formatos"""

    # Exportar a JSON
    with open(f"{archivo_base}.json", 'w', encoding='utf-8') as f:
        # Convertir objetos PerfilCiudadano a diccionarios para JSON
        resultados_json = {
            'perfiles_detallados': resultados['perfiles_detallados'],
            'metadata': resultados['metadata']
        }
        json.dump(resultados_json, f, ensure_ascii=False, indent=2)

    # Exportar seleccionados a CSV
    seleccionados_data = []
    for persona in resultados['seleccionados']:
        seleccionados_data.append({
            'id': persona.id,
            'sexo': persona.sexo,
            'edad': persona.edad,
            'generacion': persona.generacion,
            'educacion': persona.educacion,
            'origen': persona.origen,
            'condicion_laboral': persona.condicion_laboral,
            'grupo_etnico': persona.grupo_etnico,
            'clase_social': persona.clase_social,
            'region': persona.region,
            'zona': persona.zona
        })

    df_seleccionados = pd.DataFrame(seleccionados_data)
    df_seleccionados.to_csv(f"{archivo_base}_seleccionados.csv", index=False, encoding='utf-8')

    print(f"✅ Resultados exportados:")
    print(f"   • {archivo_base}.json")
    print(f"   • {archivo_base}_seleccionados.csv")

# Ejemplo de uso
if __name__ == "__main__":
    # Crear instancia del algoritmo
    algoritmo = AlgoritmoSeleccionCiudadanos()

    # Ejecutar selección completa
    resultados = algoritmo.ejecutar_seleccion_completa(n_candidatos=300)

    # Exportar resultados
    exportar_resultados(resultados)

    # Mostrar algunos perfiles ejemplo
    print("\n👥 EJEMPLOS DE PERFILES SELECCIONADOS:")
    print("=" * 80)
    for i in range(min(5, len(resultados['perfiles_detallados']['perfiles_individuales']))):
        perfil = resultados['perfiles_detallados']['perfiles_individuales'][i]
        print(f"\n{perfil['perfil_narrativo']}")

    print(f"\n📊 RESUMEN ESTADÍSTICO FINAL:")
    resumen = resultados['perfiles_detallados']['resumen_estadistico']
    print(f"   • Edad promedio: {resumen['estadisticas_edad']['promedio']} años")
    print(f"   • Rango de edad: {resumen['estadisticas_edad']['minima']}-{resumen['estadisticas_edad']['maxima']} años")
    print(f"   • Distribución por sexo: {resumen['sexo']['porcentajes']}")
    print(f"   • Distribución por educación: {resumen['educacion']['porcentajes']}")
    print(f"   • Distribución por origen: {resumen['origen']['porcentajes']}")
