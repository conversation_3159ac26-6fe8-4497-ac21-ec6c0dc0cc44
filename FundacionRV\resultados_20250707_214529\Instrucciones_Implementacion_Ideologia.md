# INSTRUCCIONES DE IMPLEMENTACIÓN: CLASIFICACIÓN IDEOLÓGICA
## Formulario Conversa Colombia - Fundación RV

### **🎯 OBJETIVO**
Integrar las nuevas preguntas de ideología política (Capítulos I, J, K) en el algoritmo de selección para garantizar diversidad ideológica en los 100 ciudadanos seleccionados.

---

## **📊 VARIABLES NUEVAS AGREGADAS AL FORMULARIO**

### **CAPÍTULO I: RELIGIÓN Y VALORES**
- **I1**: Religión/credo (15 categorías)
- **I2**: Frecuencia servicios religiosos (6 niveles)
- **I3**: Importancia religión (4 niveles)
- **I4**: Decisiones políticas basadas en religión (5 niveles)

### **CAPÍTULO J: CONSUMO DE MEDIOS**
- **J1a-J1e**: Frecuencia información por medio (6 niveles c/u)
- **J2**: Uso redes sociales (Sí/No)
- **J3**: Redes específicas usadas (múltiple)
- **J3a**: Frecuencia información política en redes (6 niveles)
- **J4a-J4c**: Confianza en medios (4 niveles c/u)
- **J5**: Medios específicos seguidos (múltiple)

### **CAPÍTULO K: ORIENTACIÓN POLÍTICA**
- **K1**: Escala ideológica directa (1-10)
- **K2a-K2e**: Preguntas proxy ideología (5 niveles c/u)
- **K3a-K3c**: Temas específicos Colombia (múltiple choice)

---

## **🧮 ALGORITMO DE CLASIFICACIÓN IDEOLÓGICA**

### **PASO 1: CALCULAR ÍNDICE IDEOLÓGICO**

```python
def calcular_indice_ideologico(respuestas):
    """
    Calcula índice ideológico final (1-10) basado en múltiples indicadores
    """
    
    # 1. ESCALA DIRECTA (40% del peso)
    escala_directa = respuestas.get('K1', 5)  # Default centro si no responde
    if escala_directa in [88, 99]:  # No sabe/No responde
        escala_directa = 5
    
    # 2. PREGUNTAS PROXY (30% del peso)
    proxy_scores = []
    
    # K2a: Estado activo economía (Pro-estado = izquierda)
    k2a = respuestas.get('K2a', 3)
    proxy_scores.append(6 - k2a if k2a <= 5 else 5)  # Invertir: 1=muy de acuerdo → 5 en escala izq
    
    # K2b: Seguridad vs libertades (Pro-seguridad = derecha)
    k2b = respuestas.get('K2b', 3)
    proxy_scores.append(k2b if k2b <= 5 else 5)  # Directo: 1=muy de acuerdo → 1 en escala izq
    
    # K2c: Valores tradicionales (Pro-tradición = derecha)
    k2c = respuestas.get('K2c', 3)
    proxy_scores.append(k2c if k2c <= 5 else 5)
    
    # K2d: Redistribución riqueza (Pro-redistribución = izquierda)
    k2d = respuestas.get('K2d', 3)
    proxy_scores.append(6 - k2d if k2d <= 5 else 5)  # Invertir
    
    # K2e: Empresas privadas eficientes (Pro-privado = derecha)
    k2e = respuestas.get('K2e', 3)
    proxy_scores.append(k2e if k2e <= 5 else 5)
    
    # Convertir a escala 1-10
    promedio_proxy = sum(proxy_scores) / len(proxy_scores)
    proxy_1_10 = (promedio_proxy - 1) * 2.25 + 1  # Escalar de 1-5 a 1-10
    
    # 3. TEMAS COLOMBIA (20% del peso)
    colombia_score = calcular_score_colombia(respuestas)
    
    # 4. FACTOR RELIGIÓN (10% del peso)
    factor_religion = calcular_factor_religion(respuestas)
    
    # ÍNDICE FINAL
    indice = (escala_directa * 0.4 + 
              proxy_1_10 * 0.3 + 
              colombia_score * 0.2 + 
              factor_religion * 0.1)
    
    return min(10, max(1, round(indice, 2)))

def calcular_score_colombia(respuestas):
    """Calcula score basado en temas específicos de Colombia"""
    
    score = 5  # Base centro
    
    # K3a: Acuerdo de Paz
    k3a = respuestas.get('K3a')
    if k3a == 1:  # Beneficioso
        score -= 1.5  # Más izquierda
    elif k3a == 3:  # No era necesario
        score += 1.5  # Más derecha
    
    # K3b: Causa violencia
    k3b = respuestas.get('K3b')
    if k3b == 1:  # Desigualdad social
        score -= 1  # Más izquierda
    elif k3b == 2:  # Falta autoridad
        score += 1  # Más derecha
    
    # K3c: Protesta social
    k3c = respuestas.get('K3c')
    if k3c == 1:  # Derecho fundamental
        score -= 1  # Más izquierda
    elif k3c == 4:  # Control firme
        score += 1  # Más derecha
    
    return min(10, max(1, score))

def calcular_factor_religion(respuestas):
    """Calcula factor religioso que influye en ideología"""
    
    religion = respuestas.get('I1')
    frecuencia = respuestas.get('I2', 6)  # Default nunca
    importancia = respuestas.get('I3', 4)  # Default nada importante
    politica_religion = respuestas.get('I4', 4)  # Default en desacuerdo
    
    factor = 5  # Base centro
    
    # Ajuste por tipo de religión
    if religion == 1:  # Católica
        if frecuencia <= 2 and importancia <= 2:  # Practicante
            factor += 1  # Más derecha
    elif religion in [2, 5, 6]:  # Protestante, Adventista, Pentecostal
        if frecuencia <= 3 and importancia <= 2:  # Practicante
            factor += 1.5  # Más derecha
    elif religion == 14:  # Ateo/agnóstico
        factor -= 1  # Más izquierda
    
    # Ajuste por influencia política de religión
    if politica_religion <= 2:  # De acuerdo con influencia religiosa
        factor += 0.5
    elif politica_religion >= 4:  # En desacuerdo
        factor -= 0.5
    
    return min(10, max(1, factor))
```

### **PASO 2: CLASIFICAR IDEOLOGÍA**

```python
def clasificar_ideologia(indice):
    """Clasifica ideología según índice calculado"""
    if indice <= 3.5:
        return 'Izquierda'
    elif indice <= 4.5:
        return 'Centro-izquierda'
    elif indice <= 5.5:
        return 'Centro'
    elif indice <= 6.5:
        return 'Centro-derecha'
    else:
        return 'Derecha'

def calcular_score_medios(respuestas):
    """Calcula score adicional basado en consumo de medios (opcional)"""
    
    medios_seguidos = respuestas.get('J5', [])
    score = 0
    
    # Medios con sesgo identificado
    medios_derecha = ['La Hora de la Verdad (Vicky Dávila)', 'Semana']
    medios_izquierda = ['Las2Orillas', 'La Silla Vacía']
    
    for medio in medios_seguidos:
        if medio in medios_derecha:
            score += 1
        elif medio in medios_izquierda:
            score -= 1
    
    return score  # Puede ser negativo, positivo o cero
```

---

## **🎯 INTEGRACIÓN CON ALGORITMO EXISTENTE**

### **MODIFICAR FUNCIÓN PRINCIPAL:**

```python
def procesar_inscripcion(datos_formulario):
    """Procesa inscripción completa incluyendo clasificación ideológica"""
    
    # Procesar datos existentes
    persona = {
        'nombre': datos_formulario['A1'],
        'edad': datos_formulario['A5'],
        'sexo': datos_formulario['B1'],
        'orientacion_sexual': datos_formulario['B2'],
        'grupo_etnico': datos_formulario['B4'],
        'educacion': datos_formulario['C2'],
        'departamento': datos_formulario['A8'],
        'origen_campesino': determinar_origen_campesino(datos_formulario),
        'condicion_laboral': determinar_condicion_laboral(datos_formulario),
        'clase_social': predecir_clase_social(datos_formulario),
        
        # NUEVAS VARIABLES IDEOLÓGICAS
        'indice_ideologico': calcular_indice_ideologico(datos_formulario),
        'clasificacion_ideologica': None,  # Se calcula después
        'religion': datos_formulario['I1'],
        'practica_religiosa': datos_formulario['I2'],
        'medios_principales': extraer_medios_principales(datos_formulario),
        'usa_redes_sociales': datos_formulario['J2'] == 1,
    }
    
    # Clasificar ideología
    persona['clasificacion_ideologica'] = clasificar_ideologia(persona['indice_ideologico'])
    
    return persona
```

### **AGREGAR DIMENSIÓN IDEOLÓGICA AL ALGORITMO:**

```python
# Modificar objetivos de representatividad
objetivos_representatividad = {
    # Dimensiones prioritarias existentes
    'sexo_orientacion': {...},
    'generacion': {...},
    'educacion': {...},
    'origen': {...},
    'condicion_laboral': {...},
    
    # NUEVA DIMENSIÓN PRIORITARIA
    'ideologia_politica': {
        'Izquierda': 20,
        'Centro-izquierda': 20,
        'Centro': 20,
        'Centro-derecha': 20,
        'Derecha': 20
    },
    
    # Dimensiones de referencia existentes
    'grupo_etnico': {...},
    'clase_social': {...}
}

def calcular_score_representatividad(muestra_seleccionada):
    """Calcula score incluyendo nueva dimensión ideológica"""
    
    scores = []
    
    # Scores existentes
    scores.append(calcular_score_sexo_orientacion(muestra_seleccionada))
    scores.append(calcular_score_generacion(muestra_seleccionada))
    scores.append(calcular_score_educacion(muestra_seleccionada))
    scores.append(calcular_score_origen(muestra_seleccionada))
    scores.append(calcular_score_condicion_laboral(muestra_seleccionada))
    
    # NUEVO SCORE IDEOLÓGICO
    scores.append(calcular_score_ideologia(muestra_seleccionada))
    
    # Scores de referencia
    scores.append(calcular_score_grupo_etnico(muestra_seleccionada) * 0.5)
    scores.append(calcular_score_clase_social(muestra_seleccionada) * 0.5)
    
    return sum(scores) / len(scores)

def calcular_score_ideologia(muestra):
    """Calcula score de representatividad ideológica"""
    
    distribucion_actual = {}
    for persona in muestra:
        ideologia = persona['clasificacion_ideologica']
        distribucion_actual[ideologia] = distribucion_actual.get(ideologia, 0) + 1
    
    objetivo = objetivos_representatividad['ideologia_politica']
    
    score = 0
    for categoria, objetivo_num in objetivo.items():
        actual = distribucion_actual.get(categoria, 0)
        diferencia = abs(actual - objetivo_num)
        score += max(0, 1 - diferencia / objetivo_num)
    
    return score / len(objetivo)
```

---

## **📊 VALIDACIÓN Y MONITOREO**

### **REPORTES AUTOMÁTICOS:**

```python
def generar_reporte_ideologico(muestra_seleccionada):
    """Genera reporte de distribución ideológica"""
    
    distribucion = {}
    indices = []
    
    for persona in muestra_seleccionada:
        ideologia = persona['clasificacion_ideologica']
        distribucion[ideologia] = distribucion.get(ideologia, 0) + 1
        indices.append(persona['indice_ideologico'])
    
    reporte = {
        'distribucion_categorias': distribucion,
        'indice_promedio': sum(indices) / len(indices),
        'indice_mediana': sorted(indices)[len(indices)//2],
        'rango_indices': (min(indices), max(indices)),
        'cumple_objetivo': all(distribucion.get(cat, 0) == 20 for cat in 
                              ['Izquierda', 'Centro-izquierda', 'Centro', 'Centro-derecha', 'Derecha'])
    }
    
    return reporte
```

### **VALIDACIÓN CRUZADA:**

```python
def validar_coherencia_ideologica(persona):
    """Valida coherencia entre diferentes indicadores ideológicos"""
    
    coherencia_score = 0
    total_checks = 0
    
    # Check 1: Religión vs ideología
    if persona['religion'] in [2, 5, 6] and persona['practica_religiosa'] <= 2:  # Evangélico practicante
        if persona['clasificacion_ideologica'] in ['Centro-derecha', 'Derecha']:
            coherencia_score += 1
        total_checks += 1
    
    # Check 2: Acuerdo de Paz vs ideología
    if 'K3a' in persona and persona['K3a'] == 1:  # Pro-paz
        if persona['clasificacion_ideologica'] in ['Izquierda', 'Centro-izquierda']:
            coherencia_score += 1
        total_checks += 1
    
    # Check 3: Medios vs ideología
    if 'Vicky Dávila' in persona.get('medios_principales', []):
        if persona['clasificacion_ideologica'] in ['Centro-derecha', 'Derecha']:
            coherencia_score += 1
        total_checks += 1
    
    return coherencia_score / max(1, total_checks) if total_checks > 0 else 1
```

---

## **⚠️ CONSIDERACIONES IMPORTANTES**

### **1. CALIBRACIÓN INICIAL:**
- **Aplicar a muestra piloto** de 200 personas
- **Validar distribución** vs encuestas nacionales (LAPOP)
- **Ajustar pesos** si es necesario

### **2. TRANSPARENCIA:**
- **Documentar metodología** completa
- **Publicar código** de clasificación
- **Permitir auditoría** externa

### **3. PRIVACIDAD:**
- **Datos agregados** únicamente en reportes
- **Anonimización** de respuestas individuales
- **Consentimiento informado** sobre uso

### **4. MONITOREO CONTINUO:**
- **Dashboard en tiempo real** de distribución
- **Alertas automáticas** si desbalance > 10%
- **Reportes diarios** durante recolección

**¡Con esta implementación garantizamos diversidad ideológica real en los 100 ciudadanos de Conversa Colombia!** 🇨🇴
