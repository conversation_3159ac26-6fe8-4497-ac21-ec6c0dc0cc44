#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simulación para determinar el tamaño óptimo del pool de candidatos
Conversa Colombia - Fundación RV
"""

from algoritmo_seleccion_ciudadanos import AlgoritmoSeleccionCiudadanos
import time

def simular_diferentes_pools():
    """Simula el algoritmo con diferentes tamaños de pool"""
    
    algoritmo = AlgoritmoSeleccionCiudadanos()
    
    # Diferentes tamaños de pool a probar
    tamaños_pool = [100, 200, 300, 500, 1000, 1500, 2000]
    
    resultados = []
    
    print("🧪 SIMULACIÓN: ¿Cuántos candidatos necesitamos?")
    print("=" * 60)
    
    for tamaño in tamaños_pool:
        print(f"\n🔍 Probando con {tamaño} candidatos...")
        
        # Medir tiempo de ejecución
        inicio = time.time()
        
        try:
            # Generar candidatos
            candidatos = algoritmo.generar_pool_candidatos(tamaño)
            
            # Ejecutar selección (menos generaciones para rapidez)
            seleccionados = algoritmo.algoritmo_genetico_seleccion(
                candidatos, 
                generaciones=50,  # Menos generaciones para rapidez
                poblacion_size=30
            )
            
            # Calcular score
            score = algoritmo.calcular_score_representatividad(seleccionados)
            
            fin = time.time()
            tiempo = fin - inicio
            
            resultado = {
                'pool_size': tamaño,
                'score': score,
                'tiempo_segundos': round(tiempo, 2),
                'exito': score >= 0.95  # Consideramos éxito si score >= 95%
            }
            
            resultados.append(resultado)
            
            # Mostrar resultado
            emoji = "✅" if resultado['exito'] else "❌"
            print(f"   {emoji} Score: {score:.3f} | Tiempo: {tiempo:.1f}s")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            resultados.append({
                'pool_size': tamaño,
                'score': 0,
                'tiempo_segundos': 0,
                'exito': False
            })
    
    return resultados

def analizar_resultados(resultados):
    """Analiza los resultados de la simulación"""
    
    print("\n" + "=" * 60)
    print("📊 ANÁLISIS DE RESULTADOS")
    print("=" * 60)
    
    print("\n| POOL SIZE | SCORE | TIEMPO | ÉXITO | RECOMENDACIÓN |")
    print("|-----------|-------|--------|-------|---------------|")
    
    for r in resultados:
        emoji_exito = "✅" if r['exito'] else "❌"
        
        # Determinar recomendación
        if r['pool_size'] < 200:
            recomendacion = "❌ Muy pequeño"
        elif r['pool_size'] <= 300:
            recomendacion = "✅ Óptimo" if r['exito'] else "⚠️ Límite"
        elif r['pool_size'] <= 500:
            recomendacion = "✅ Excelente" if r['exito'] else "⚠️ Revisar"
        elif r['pool_size'] <= 1000:
            recomendacion = "✅ Muy bueno" if r['exito'] else "⚠️ Problema"
        else:
            recomendacion = "⚠️ Excesivo" if r['exito'] else "❌ Problema"
        
        print(f"| {r['pool_size']:9d} | {r['score']:.3f} | {r['tiempo_segundos']:6.1f}s | {emoji_exito:5s} | {recomendacion:13s} |")
    
    # Encontrar el pool mínimo exitoso
    pools_exitosos = [r for r in resultados if r['exito']]
    
    if pools_exitosos:
        pool_minimo = min(pools_exitosos, key=lambda x: x['pool_size'])
        pool_optimo = min(pools_exitosos, key=lambda x: x['tiempo_segundos'])
        
        print(f"\n🎯 RECOMENDACIONES:")
        print(f"   • Pool MÍNIMO exitoso: {pool_minimo['pool_size']} candidatos")
        print(f"   • Pool ÓPTIMO (tiempo/calidad): {pool_optimo['pool_size']} candidatos")
        print(f"   • Pool RECOMENDADO para producción: {pool_optimo['pool_size'] * 2} candidatos")
        
        return pool_optimo['pool_size']
    else:
        print("\n❌ PROBLEMA: Ningún pool logró score satisfactorio")
        return None

if __name__ == "__main__":
    # Ejecutar simulación
    resultados = simular_diferentes_pools()
    
    # Analizar resultados
    pool_recomendado = analizar_resultados(resultados)
    
    if pool_recomendado:
        print(f"\n🚀 CONCLUSIÓN FINAL:")
        print(f"   Para garantizar una selección perfecta de 100 ciudadanos,")
        print(f"   necesitas inscribir AL MENOS {pool_recomendado * 2} personas.")
        print(f"   Recomendación: {pool_recomendado * 3} personas para mayor seguridad.")
