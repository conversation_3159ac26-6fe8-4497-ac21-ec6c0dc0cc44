Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEF4D70000 ntdll.dll
7FFEF2D60000 KERNEL32.DLL
7FFEF2120000 KERNELBASE.dll
7FFEEE5E0000 apphelp.dll
7FFEF2B30000 USER32.dll
7FFEF2560000 win32u.dll
7FFEF3AB0000 GDI32.dll
7FFEF2810000 gdi32full.dll
7FFEF2080000 msvcp_win.dll
7FFEF2590000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEF3FA0000 advapi32.dll
7FFEF4C60000 msvcrt.dll
7FFEF3310000 sechost.dll
7FFEF26B0000 bcrypt.dll
7FFEF3940000 RPCRT4.dll
7FFEF1910000 CRYPTBASE.DLL
7FFEF24E0000 bcryptPrimitives.dll
7FFEF3A70000 IMM32.DLL
